import os

# Replace with your actual dataset path
dataset_path = r"C:\Users\<USER>\Downloads\Bone Break Classification.v2i.folder"

print("Folders in dataset:")
for item in os.listdir(dataset_path):
    if os.path.isdir(os.path.join(dataset_path, item)):
        print(f"- {item}")
        subfolders = os.listdir(os.path.join(dataset_path, item))
        for subfolder in subfolders:
            if os.path.isdir(os.path.join(dataset_path, item, subfolder)):
                print(f"  - {subfolder}")