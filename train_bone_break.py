import tensorflow as tf
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.applications import MobileNetV2
from tensorflow.keras.layers import Dense, GlobalAveragePooling2D
from tensorflow.keras.models import Model

# Set paths
# Update this to your actual dataset location
DATASET_PATH = r"C:\Users\<USER>\Downloads\Bone Break Classification.v2i.folder"

# Data generators with augmentation matching the Roboflow preprocessing
train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,
    brightness_range=[0.75, 1.25],
    validation_split=0.2
)

# Load training data
train_generator = train_datagen.flow_from_directory(
    DATASET_PATH + '/train',
    target_size=(640, 640),
    batch_size=16,
    class_mode='categorical'
)

# Load validation data
validation_generator = train_datagen.flow_from_directory(
    DATASET_PATH + '/valid',
    target_size=(640, 640),
    batch_size=16,
    class_mode='categorical'
)

# Create model
base_model = MobileNetV2(weights='imagenet', include_top=False, input_shape=(640, 640, 3))
x = base_model.output
x = GlobalAveragePooling2D()(x)
x = Dense(128, activation='relu')(x)
predictions = Dense(train_generator.num_classes, activation='softmax')(x)
model = Model(inputs=base_model.input, outputs=predictions)

# Compile model
model.compile(
    optimizer='adam',
    loss='categorical_crossentropy',
    metrics=['accuracy']
)

# Train model
history = model.fit(
    train_generator,
    steps_per_epoch=train_generator.samples // 16,
    validation_data=validation_generator,
    validation_steps=validation_generator.samples // 16,
    epochs=10
)

# Save model
model.save('bone_break_model.h5')
print("Model trained and saved!")
